#!/usr/bin/env python3
"""
Enhanced Database Schema for Playwright Integration
Extends existing crawl4ai database with Playwright-specific metrics
"""

import sqlite3
import json
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedDatabase:
    """Enhanced database for storing both crawl4ai and Playwright test results"""
    
    def __init__(self, db_path: str = "playwright_integration_tests.db"):
        self.db_path = db_path
        self.db_lock = threading.Lock()
        self.init_database()
    
    def init_database(self):
        """Initialize database with enhanced schema"""
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Enhanced test_results table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_id TEXT,
                    test_name TEXT,
                    url TEXT NOT NULL,
                    domain TEXT,
                    status TEXT,
                    method TEXT,
                    execution_time REAL,
                    extraction_rate REAL,
                    error_message TEXT,
                    metadata TEXT,
                    timestamp TEXT,
                    
                    -- Basic content metrics
                    title TEXT,
                    meta_description TEXT,
                    word_count INTEGER DEFAULT 0,
                    paragraphs INTEGER DEFAULT 0,
                    language TEXT,
                    content_quality_score INTEGER DEFAULT 0,
                    
                    -- Structure metrics
                    headings_h1 INTEGER DEFAULT 0,
                    headings_h2 INTEGER DEFAULT 0,
                    headings_h3 INTEGER DEFAULT 0,
                    headings_h4 INTEGER DEFAULT 0,
                    headings_h5 INTEGER DEFAULT 0,
                    headings_h6 INTEGER DEFAULT 0,
                    links_total INTEGER DEFAULT 0,
                    links_internal INTEGER DEFAULT 0,
                    links_external INTEGER DEFAULT 0,
                    images_total INTEGER DEFAULT 0,
                    images_with_alt INTEGER DEFAULT 0,
                    images_without_alt INTEGER DEFAULT 0,
                    
                    -- Technology detection
                    technology_stack TEXT,
                    frameworks_detected TEXT,
                    social_media TEXT,
                    emails TEXT,
                    phone_numbers TEXT,
                    structured_data BOOLEAN DEFAULT 0,
                    readability_score REAL DEFAULT 0,
                    
                    -- Playwright-specific metrics
                    browser_engine TEXT,
                    javascript_errors TEXT,
                    network_requests INTEGER DEFAULT 0,
                    load_time REAL DEFAULT 0,
                    screenshot_path TEXT,
                    protection_bypassed BOOLEAN DEFAULT 0,
                    protection_type TEXT,
                    spa_detected BOOLEAN DEFAULT 0,
                    ajax_content INTEGER DEFAULT 0,
                    modal_dialogs INTEGER DEFAULT 0,
                    
                    -- Performance metrics
                    response_time REAL DEFAULT 0,
                    status_code INTEGER DEFAULT 0,
                    final_method TEXT
                )
            """)
            
            # Website classifications table (enhanced)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS website_classifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    domain TEXT UNIQUE,
                    protection_type TEXT,
                    technology_stack TEXT,
                    authentication_required BOOLEAN DEFAULT 0,
                    rate_limited BOOLEAN DEFAULT 0,
                    javascript_heavy BOOLEAN DEFAULT 0,
                    spa_framework BOOLEAN DEFAULT 0,
                    cloudflare_protected BOOLEAN DEFAULT 0,
                    akamai_protected BOOLEAN DEFAULT 0,
                    captcha_required BOOLEAN DEFAULT 0,
                    
                    -- Success rates by method
                    crawl4ai_success_rate REAL DEFAULT 0,
                    playwright_success_rate REAL DEFAULT 0,
                    recommended_method TEXT,
                    
                    -- Last updated
                    last_tested TEXT,
                    test_count INTEGER DEFAULT 0,
                    
                    -- Performance characteristics
                    avg_response_time REAL DEFAULT 0,
                    avg_quality_score REAL DEFAULT 0,
                    reliability_score REAL DEFAULT 0
                )
            """)
            
            # Method comparison table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS method_comparisons (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    url TEXT,
                    domain TEXT,
                    test_date TEXT,
                    
                    -- Crawl4ai results
                    crawl4ai_status TEXT,
                    crawl4ai_quality_score INTEGER DEFAULT 0,
                    crawl4ai_execution_time REAL DEFAULT 0,
                    crawl4ai_word_count INTEGER DEFAULT 0,
                    crawl4ai_error TEXT,
                    
                    -- Playwright results
                    playwright_status TEXT,
                    playwright_quality_score INTEGER DEFAULT 0,
                    playwright_execution_time REAL DEFAULT 0,
                    playwright_word_count INTEGER DEFAULT 0,
                    playwright_error TEXT,
                    
                    -- Comparison metrics
                    quality_improvement INTEGER DEFAULT 0,
                    speed_ratio REAL DEFAULT 0,
                    success_improvement BOOLEAN DEFAULT 0,
                    recommended_method TEXT
                )
            """)
            
            # Performance benchmarks table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS performance_benchmarks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_session TEXT,
                    test_date TEXT,
                    total_tests INTEGER,
                    
                    -- Overall metrics
                    overall_success_rate REAL,
                    avg_execution_time REAL,
                    avg_quality_score REAL,
                    
                    -- Method-specific metrics
                    crawl4ai_tests INTEGER DEFAULT 0,
                    crawl4ai_successes INTEGER DEFAULT 0,
                    crawl4ai_avg_time REAL DEFAULT 0,
                    crawl4ai_avg_quality REAL DEFAULT 0,
                    
                    playwright_tests INTEGER DEFAULT 0,
                    playwright_successes INTEGER DEFAULT 0,
                    playwright_avg_time REAL DEFAULT 0,
                    playwright_avg_quality REAL DEFAULT 0,
                    
                    -- Protection type breakdown
                    cloudflare_tests INTEGER DEFAULT 0,
                    cloudflare_successes INTEGER DEFAULT 0,
                    akamai_tests INTEGER DEFAULT 0,
                    akamai_successes INTEGER DEFAULT 0,
                    standard_tests INTEGER DEFAULT 0,
                    standard_successes INTEGER DEFAULT 0,
                    
                    -- Resource usage
                    memory_usage_mb REAL DEFAULT 0,
                    cpu_usage_percent REAL DEFAULT 0,
                    
                    -- Configuration
                    worker_count INTEGER DEFAULT 1,
                    browser_type TEXT,
                    headless_mode BOOLEAN DEFAULT 1
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_results_url ON test_results(url)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_results_domain ON test_results(domain)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_results_method ON test_results(method)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_results_status ON test_results(status)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_test_results_timestamp ON test_results(timestamp)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_classifications_domain ON website_classifications(domain)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_comparisons_domain ON method_comparisons(domain)")
            
            conn.commit()
            conn.close()
            
            logger.info(f"Enhanced database initialized: {self.db_path}")
    
    def save_test_result(self, result: Dict[str, Any], worker_id: int = 0) -> bool:
        """Save test result with enhanced metrics"""
        
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                
                # Extract domain from URL
                from urllib.parse import urlparse
                domain = urlparse(result.get('url', '')).netloc.replace('www.', '')
                
                # Prepare data for insertion
                data = (
                    result.get('test_id', f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                    result.get('test_name', 'playwright_integration_test'),
                    result.get('url', ''),
                    domain,
                    result.get('status', 'UNKNOWN'),
                    result.get('method', 'unknown'),
                    result.get('execution_time', 0),
                    result.get('extraction_rate', 0),
                    result.get('error_message', ''),
                    json.dumps(result.get('metadata', {})),
                    result.get('timestamp', datetime.now().isoformat()),
                    
                    # Basic content
                    result.get('title', ''),
                    result.get('meta_description', ''),
                    result.get('word_count', 0),
                    result.get('paragraphs', 0),
                    result.get('language', 'unknown'),
                    result.get('content_quality_score', 0),
                    
                    # Structure
                    result.get('headings', {}).get('h1', 0),
                    result.get('headings', {}).get('h2', 0),
                    result.get('headings', {}).get('h3', 0),
                    result.get('headings', {}).get('h4', 0),
                    result.get('headings', {}).get('h5', 0),
                    result.get('headings', {}).get('h6', 0),
                    result.get('links', {}).get('total', 0),
                    result.get('links', {}).get('internal', 0),
                    result.get('links', {}).get('external', 0),
                    result.get('images', {}).get('total', 0),
                    result.get('images', {}).get('with_alt', 0),
                    result.get('images', {}).get('without_alt', 0),
                    
                    # Technology
                    json.dumps(result.get('technology_stack', [])),
                    json.dumps(result.get('frameworks_detected', [])),
                    json.dumps(result.get('social_media', [])),
                    json.dumps(result.get('emails', [])),
                    json.dumps(result.get('phone_numbers', [])),
                    result.get('structured_data', False),
                    result.get('readability_score', 0),
                    
                    # Playwright-specific
                    result.get('browser_engine', ''),
                    json.dumps(result.get('javascript_errors', [])),
                    result.get('network_requests', 0),
                    result.get('load_time', 0),
                    result.get('screenshot_path', ''),
                    result.get('protection_bypassed', False),
                    result.get('protection_type', 'unknown'),
                    result.get('spa_detected', False),
                    result.get('ajax_content', 0),
                    result.get('modal_dialogs', 0),
                    
                    # Performance
                    result.get('response_time', 0),
                    result.get('status_code', 0),
                    result.get('final_method', result.get('method', 'unknown'))
                )
                
                cursor.execute("""
                    INSERT INTO test_results (
                        test_id, test_name, url, domain, status, method, execution_time,
                        extraction_rate, error_message, metadata, timestamp,
                        title, meta_description, word_count, paragraphs, language, content_quality_score,
                        headings_h1, headings_h2, headings_h3, headings_h4, headings_h5, headings_h6,
                        links_total, links_internal, links_external,
                        images_total, images_with_alt, images_without_alt,
                        technology_stack, frameworks_detected, social_media, emails, phone_numbers,
                        structured_data, readability_score,
                        browser_engine, javascript_errors, network_requests, load_time, screenshot_path,
                        protection_bypassed, protection_type, spa_detected, ajax_content, modal_dialogs,
                        response_time, status_code, final_method
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, data)
                
                # Update website classification if successful
                if result.get('status') == 'PASS':
                    self._update_website_classification(cursor, domain, result)
                
                conn.commit()
                conn.close()
                
                return True
                
        except Exception as e:
            logger.error(f"Error saving test result: {str(e)}")
            return False
    
    def _update_website_classification(self, cursor, domain: str, result: Dict):
        """Update website classification based on test result"""
        
        try:
            # Get existing classification
            cursor.execute("SELECT * FROM website_classifications WHERE domain = ?", (domain,))
            existing = cursor.fetchone()
            
            if existing:
                # Update existing record
                cursor.execute("""
                    UPDATE website_classifications SET
                        protection_type = ?,
                        technology_stack = ?,
                        javascript_heavy = ?,
                        spa_framework = ?,
                        cloudflare_protected = ?,
                        captcha_required = ?,
                        last_tested = ?,
                        test_count = test_count + 1,
                        avg_quality_score = (avg_quality_score * (test_count - 1) + ?) / test_count
                    WHERE domain = ?
                """, (
                    result.get('protection_type', 'unknown'),
                    json.dumps(result.get('technology_stack', [])),
                    len(result.get('javascript_errors', [])) > 0,
                    result.get('spa_detected', False),
                    result.get('protection_type') == 'cloudflare',
                    result.get('protection_type') == 'captcha',
                    datetime.now().isoformat(),
                    result.get('content_quality_score', 0),
                    domain
                ))
            else:
                # Insert new record
                cursor.execute("""
                    INSERT INTO website_classifications (
                        domain, protection_type, technology_stack, javascript_heavy,
                        spa_framework, cloudflare_protected, captcha_required,
                        last_tested, test_count, avg_quality_score
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    domain,
                    result.get('protection_type', 'unknown'),
                    json.dumps(result.get('technology_stack', [])),
                    len(result.get('javascript_errors', [])) > 0,
                    result.get('spa_detected', False),
                    result.get('protection_type') == 'cloudflare',
                    result.get('protection_type') == 'captcha',
                    datetime.now().isoformat(),
                    1,
                    result.get('content_quality_score', 0)
                ))
                
        except Exception as e:
            logger.warning(f"Error updating website classification: {str(e)}")
    
    def get_test_summary(self) -> Dict[str, Any]:
        """Get comprehensive test summary statistics"""
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            summary = {}
            
            # Overall statistics
            cursor.execute("SELECT COUNT(*) FROM test_results")
            summary['total_tests'] = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM test_results WHERE status = 'PASS'")
            summary['successful_tests'] = cursor.fetchone()[0]
            
            summary['overall_success_rate'] = summary['successful_tests'] / max(1, summary['total_tests'])
            
            # Method comparison
            cursor.execute("""
                SELECT method, COUNT(*) as count, 
                       AVG(CASE WHEN status = 'PASS' THEN 1.0 ELSE 0.0 END) as success_rate,
                       AVG(execution_time) as avg_time,
                       AVG(content_quality_score) as avg_quality
                FROM test_results 
                GROUP BY method
            """)
            
            summary['method_stats'] = {}
            for row in cursor.fetchall():
                method, count, success_rate, avg_time, avg_quality = row
                summary['method_stats'][method] = {
                    'count': count,
                    'success_rate': success_rate or 0,
                    'avg_execution_time': avg_time or 0,
                    'avg_quality_score': avg_quality or 0
                }
            
            # Protection type analysis
            cursor.execute("""
                SELECT protection_type, COUNT(*) as count,
                       AVG(CASE WHEN status = 'PASS' THEN 1.0 ELSE 0.0 END) as success_rate
                FROM test_results 
                WHERE protection_type != 'unknown'
                GROUP BY protection_type
            """)
            
            summary['protection_stats'] = {}
            for row in cursor.fetchall():
                ptype, count, success_rate = row
                summary['protection_stats'][ptype] = {
                    'count': count,
                    'success_rate': success_rate or 0
                }
            
            conn.close()
            
            return summary
    
    def export_results_to_json(self, output_file: str = "test_results_export.json"):
        """Export all test results to JSON for analysis"""
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM test_results ORDER BY timestamp DESC")
            columns = [description[0] for description in cursor.description]
            
            results = []
            for row in cursor.fetchall():
                result = dict(zip(columns, row))
                
                # Parse JSON fields
                for field in ['metadata', 'technology_stack', 'frameworks_detected', 
                             'social_media', 'emails', 'phone_numbers', 'javascript_errors']:
                    if result.get(field):
                        try:
                            result[field] = json.loads(result[field])
                        except:
                            pass
                
                results.append(result)
            
            conn.close()
            
            # Save to file
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            
            logger.info(f"Exported {len(results)} test results to {output_file}")
            
            return results

# Test function
def test_enhanced_database():
    """Test the enhanced database functionality"""
    
    db = EnhancedDatabase("test_enhanced.db")
    
    # Test data
    test_result = {
        'url': 'https://example.com',
        'status': 'PASS',
        'method': 'playwright',
        'execution_time': 2.5,
        'title': 'Test Site',
        'word_count': 500,
        'content_quality_score': 75,
        'browser_engine': 'chromium',
        'protection_type': 'cloudflare',
        'protection_bypassed': True,
        'technology_stack': ['React', 'Bootstrap'],
        'javascript_errors': ['Console error: test'],
        'timestamp': datetime.now().isoformat()
    }
    
    # Save test result
    success = db.save_test_result(test_result)
    print(f"Save result: {success}")
    
    # Get summary
    summary = db.get_test_summary()
    print(f"Summary: {summary}")
    
    # Export results
    results = db.export_results_to_json("test_export.json")
    print(f"Exported {len(results)} results")

if __name__ == "__main__":
    test_enhanced_database()
