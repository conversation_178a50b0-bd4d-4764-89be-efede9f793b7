#!/usr/bin/env python3
"""
Protection Detection System for Intelligent Routing
Determines optimal extraction method (crawl4<PERSON> vs <PERSON><PERSON>) based on site characteristics
"""

import requests
import re
import time
from urllib.parse import urlparse
from typing import Dict, List, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ProtectionDetector:
    """Intelligent system to detect website protection mechanisms and route to optimal extractor"""
    
    def __init__(self):
        # User agents for detection requests
        self.user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/******** Firefox/120.0'
        ]
        
        # Protection patterns
        self.protection_patterns = {
            'cloudflare': [
                'cloudflare', 'cf-ray', '__cf_bm', 'checking your browser',
                'ddos protection by cloudflare', 'challenge-platform',
                'cf-browser-verification', 'cf-challenge'
            ],
            'akamai': [
                'akamai', 'akamai technologies', 'reference #',
                'akamai ghost', 'bot manager'
            ],
            'captcha': [
                'captcha', 'recaptcha', 'hcaptcha', 'verify you are human',
                'prove you are not a robot', 'security check'
            ],
            'rate_limited': [
                'rate limit', 'too many requests', '429', 'slow down',
                'request limit exceeded', 'throttled'
            ],
            'custom_protection': [
                'access denied', 'blocked', 'forbidden', 'security',
                'anti-bot', 'bot detection', 'suspicious activity'
            ]
        }
        
        # Known protected domains (from previous testing)
        self.known_protected_domains = {
            'cloudflare': [
                'canva.com', 'udemy.com', 'kickstarter.com', 
                'technewsworld.com', 'cruisecritic.com'
            ],
            'akamai': [
                'apple.com', 'microsoft.com', 'adobe.com'
            ],
            'heavy_js': [
                'react.dev', 'vuejs.org', 'angular.io'
            ]
        }
        
        # Routing rules
        self.routing_rules = {
            'cloudflare': 'playwright_primary',
            'akamai': 'playwright_primary',
            'captcha': 'playwright_primary',
            'heavy_js': 'playwright_primary',
            'spa_framework': 'playwright_primary',
            'rate_limited': 'crawl4ai_with_delays',
            'custom_protection': 'crawl4ai_fallback_playwright',
            'standard': 'crawl4ai_primary_playwright_fallback',
            'api_endpoint': 'crawl4ai_only'
        }
    
    def quick_protection_check(self, url: str, timeout: int = 10) -> Dict[str, any]:
        """Quick check to detect protection type without full page load"""
        
        result = {
            'url': url,
            'protection_type': 'unknown',
            'confidence': 0.0,
            'recommended_method': 'crawl4ai_primary_playwright_fallback',
            'indicators': [],
            'response_time': 0,
            'status_code': 0,
            'headers': {},
            'error': None
        }
        
        start_time = time.time()
        
        try:
            # Check if domain is in known protected list
            domain = urlparse(url).netloc.lower().replace('www.', '')
            for protection_type, domains in self.known_protected_domains.items():
                if any(known_domain in domain for known_domain in domains):
                    result['protection_type'] = protection_type
                    result['confidence'] = 0.9
                    result['recommended_method'] = self.routing_rules.get(protection_type, 'playwright_primary')
                    result['indicators'].append(f'Known {protection_type} domain')
                    return result
            
            # Make HEAD request first (faster)
            headers = {
                'User-Agent': self.user_agents[0],
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # Try HEAD request first
            try:
                response = requests.head(url, headers=headers, timeout=timeout, allow_redirects=True)
                result['status_code'] = response.status_code
                result['headers'] = dict(response.headers)
                
                # Check headers for protection indicators
                protection_type, confidence = self._analyze_headers(response.headers)
                if protection_type != 'unknown':
                    result['protection_type'] = protection_type
                    result['confidence'] = confidence
                    result['recommended_method'] = self.routing_rules.get(protection_type, 'playwright_primary')
                    return result
                
            except requests.exceptions.RequestException:
                # HEAD request failed, try GET
                pass
            
            # If HEAD didn't work or didn't reveal protection, try GET with limited content
            try:
                response = requests.get(
                    url, 
                    headers=headers, 
                    timeout=timeout, 
                    allow_redirects=True,
                    stream=True
                )
                
                result['status_code'] = response.status_code
                result['headers'] = dict(response.headers)
                
                # Read first 10KB of content for analysis
                content_sample = ""
                for chunk in response.iter_content(chunk_size=1024):
                    content_sample += chunk.decode('utf-8', errors='ignore')
                    if len(content_sample) > 10240:  # 10KB limit
                        break
                
                # Analyze content for protection patterns
                protection_type, confidence, indicators = self._analyze_content(content_sample)
                result['protection_type'] = protection_type
                result['confidence'] = confidence
                result['indicators'] = indicators
                result['recommended_method'] = self.routing_rules.get(protection_type, 'crawl4ai_primary_playwright_fallback')
                
            except requests.exceptions.RequestException as e:
                result['error'] = str(e)
                # If we can't even connect, might be heavily protected
                result['protection_type'] = 'custom_protection'
                result['confidence'] = 0.7
                result['recommended_method'] = 'playwright_primary'
                result['indicators'].append(f'Connection failed: {str(e)}')
        
        except Exception as e:
            result['error'] = str(e)
            logger.warning(f"Error in protection detection for {url}: {str(e)}")
        
        finally:
            result['response_time'] = time.time() - start_time
        
        return result
    
    def _analyze_headers(self, headers: Dict[str, str]) -> Tuple[str, float]:
        """Analyze HTTP headers for protection indicators"""
        
        headers_lower = {k.lower(): v.lower() for k, v in headers.items()}
        
        # Cloudflare indicators
        cloudflare_headers = ['cf-ray', 'cf-cache-status', 'cf-request-id', 'server']
        if any(header in headers_lower for header in cloudflare_headers):
            if 'cloudflare' in headers_lower.get('server', ''):
                return 'cloudflare', 0.9
            elif any(headers_lower.get(header, '') for header in cloudflare_headers[:3]):
                return 'cloudflare', 0.8
        
        # Akamai indicators
        if 'akamai' in headers_lower.get('server', '') or 'akamai' in str(headers_lower):
            return 'akamai', 0.8
        
        # Rate limiting indicators
        if 'x-ratelimit' in str(headers_lower) or 'retry-after' in headers_lower:
            return 'rate_limited', 0.7
        
        return 'unknown', 0.0
    
    def _analyze_content(self, content: str) -> Tuple[str, float, List[str]]:
        """Analyze page content for protection patterns"""
        
        content_lower = content.lower()
        indicators = []
        
        # Check each protection type
        for protection_type, patterns in self.protection_patterns.items():
            matches = []
            for pattern in patterns:
                if pattern in content_lower:
                    matches.append(pattern)
            
            if matches:
                confidence = min(0.9, len(matches) * 0.3)  # More matches = higher confidence
                indicators.extend(matches)
                return protection_type, confidence, indicators
        
        # Check for SPA frameworks (require JavaScript)
        spa_patterns = [
            'react', 'vue.js', 'angular', 'ember.js', 'backbone.js',
            'single page application', 'spa', '__react', '__vue__'
        ]
        
        spa_matches = [pattern for pattern in spa_patterns if pattern in content_lower]
        if spa_matches:
            indicators.extend(spa_matches)
            return 'spa_framework', 0.6, indicators
        
        # Check for heavy JavaScript usage
        js_patterns = [
            'document.getelementbyid', 'jquery', 'ajax', 'fetch(',
            'xmlhttprequest', 'async function', 'await '
        ]
        
        js_matches = [pattern for pattern in js_patterns if pattern in content_lower]
        if len(js_matches) >= 3:
            indicators.extend(js_matches)
            return 'heavy_js', 0.5, indicators
        
        # Check if it looks like an API endpoint
        if any(indicator in content_lower for indicator in [
            '"status":', '"data":', '"error":', '"message":',
            'application/json', 'api response'
        ]):
            indicators.append('API endpoint detected')
            return 'api_endpoint', 0.7, indicators
        
        return 'standard', 0.3, indicators
    
    def get_extraction_strategy(self, url: str) -> Dict[str, any]:
        """Get complete extraction strategy for a URL"""
        
        detection_result = self.quick_protection_check(url)
        
        strategy = {
            'url': url,
            'detection_result': detection_result,
            'primary_method': None,
            'fallback_method': None,
            'special_config': {},
            'expected_success_rate': 0.0
        }
        
        method = detection_result['recommended_method']
        
        if method == 'playwright_primary':
            strategy['primary_method'] = 'playwright'
            strategy['fallback_method'] = None
            strategy['expected_success_rate'] = 0.85
            
        elif method == 'crawl4ai_primary_playwright_fallback':
            strategy['primary_method'] = 'crawl4ai'
            strategy['fallback_method'] = 'playwright'
            strategy['expected_success_rate'] = 0.75
            
        elif method == 'crawl4ai_with_delays':
            strategy['primary_method'] = 'crawl4ai'
            strategy['fallback_method'] = 'playwright'
            strategy['special_config'] = {'delay_between_requests': 3.0, 'max_retries': 5}
            strategy['expected_success_rate'] = 0.60
            
        elif method == 'crawl4ai_fallback_playwright':
            strategy['primary_method'] = 'crawl4ai'
            strategy['fallback_method'] = 'playwright'
            strategy['expected_success_rate'] = 0.70
            
        elif method == 'crawl4ai_only':
            strategy['primary_method'] = 'crawl4ai'
            strategy['fallback_method'] = None
            strategy['expected_success_rate'] = 0.95
            
        else:
            # Default strategy
            strategy['primary_method'] = 'crawl4ai'
            strategy['fallback_method'] = 'playwright'
            strategy['expected_success_rate'] = 0.70
        
        return strategy
    
    def batch_analyze_urls(self, urls: List[str]) -> Dict[str, Dict]:
        """Analyze multiple URLs and return routing strategies"""
        
        logger.info(f"Analyzing {len(urls)} URLs for protection detection...")
        
        results = {}
        
        for i, url in enumerate(urls):
            try:
                strategy = self.get_extraction_strategy(url)
                results[url] = strategy
                
                if (i + 1) % 10 == 0:
                    logger.info(f"Analyzed {i + 1}/{len(urls)} URLs")
                
                # Small delay to be respectful
                time.sleep(0.1)
                
            except Exception as e:
                logger.warning(f"Error analyzing {url}: {str(e)}")
                results[url] = {
                    'url': url,
                    'detection_result': {'error': str(e)},
                    'primary_method': 'crawl4ai',
                    'fallback_method': 'playwright',
                    'expected_success_rate': 0.5
                }
        
        # Summary statistics
        method_counts = {}
        for result in results.values():
            method = result.get('primary_method', 'unknown')
            method_counts[method] = method_counts.get(method, 0) + 1
        
        logger.info("Protection analysis complete:")
        for method, count in method_counts.items():
            logger.info(f"  {method}: {count} URLs")
        
        return results

# Test function
def test_protection_detector():
    """Test the protection detector with known sites"""
    
    detector = ProtectionDetector()
    
    test_urls = [
        "https://canva.com",  # Known Cloudflare
        "https://udemy.com",  # Known Cloudflare
        "https://httpbin.org/get",  # API endpoint
        "https://example.com",  # Standard site
        "https://react.dev"  # SPA framework
    ]
    
    print("=== Protection Detection Test ===")
    
    for url in test_urls:
        print(f"\nTesting: {url}")
        strategy = detector.get_extraction_strategy(url)
        
        detection = strategy['detection_result']
        print(f"Protection type: {detection['protection_type']}")
        print(f"Confidence: {detection['confidence']:.2f}")
        print(f"Primary method: {strategy['primary_method']}")
        print(f"Fallback method: {strategy['fallback_method']}")
        print(f"Expected success: {strategy['expected_success_rate']:.0%}")
        
        if detection['indicators']:
            print(f"Indicators: {', '.join(detection['indicators'][:3])}")

if __name__ == "__main__":
    test_protection_detector()
