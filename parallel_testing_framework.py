#!/usr/bin/env python3
"""
Parallel Testing Framework for Playwright Integration
20-worker architecture for high-throughput testing of 300+ websites
"""

import asyncio
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from pathlib import Path

# Import our components
from hybrid_crawler import HybridCrawler
from enhanced_database import EnhancedDatabase
from google_search_api import CustomSearchAPI

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParallelTestingFramework:
    """High-performance parallel testing framework with 20 workers"""

    def __init__(self, num_workers: int = 20, target_cache: int = 300):
        self.num_workers = num_workers
        self.target_cache = target_cache

        # Work distribution
        self.work_queue = Queue()
        self.results_queue = Queue()
        self.completed_urls = set()

        # Components
        self.database = EnhancedDatabase()
        self.search_api = CustomSearchAPI()

        # Worker management
        self.workers = []
        self.worker_stats = {}
        self.active_workers = 0

        # Progress tracking
        self.total_urls = 0
        self.processed_urls = 0
        self.successful_tests = 0
        self.failed_tests = 0
        self.start_time = None

        # Thread safety
        self.stats_lock = threading.Lock()
        self.progress_lock = threading.Lock()

        # Results storage
        self.all_results = []

        # Configuration
        self.max_browser_instances = 5  # Limit concurrent browsers
        self.browser_semaphore = asyncio.Semaphore(self.max_browser_instances)

    def prepare_test_urls(self) -> List[str]:
        """Prepare comprehensive list of URLs for testing"""

        logger.info("Preparing test URL corpus...")

        # Priority Cloudflare sites (known failures)
        priority_sites = [
            "https://canva.com",
            "https://udemy.com",
            "https://kickstarter.com",
            "https://technewsworld.com",
            "https://cruisecritic.com"
        ]

        # Get existing cache (simulate 132 existing sites)
        existing_cache = [
            "https://httpbin.org/get",
            "https://example.com",
            "https://stackoverflow.com",
            "https://github.com",
            "https://reddit.com",
            "https://news.ycombinator.com",
            "https://reuters.com",
            "https://w3.org",
            "https://python.org",
            "https://nodejs.org"
        ] * 13  # Simulate 130 existing sites

        # Discover new sites via Google Search API
        logger.info("Discovering new protected sites...")
        corpus = self.search_api.prepare_test_corpus(max_new_sites=200)
        new_discoveries = [f"https://{domain}" for domain in corpus['new_discoveries']]

        # Combine all URLs
        all_urls = priority_sites + existing_cache + new_discoveries

        # Remove duplicates while preserving order
        seen = set()
        unique_urls = []
        for url in all_urls:
            if url not in seen:
                seen.add(url)
                unique_urls.append(url)

        # Limit to target cache size
        final_urls = unique_urls[:self.target_cache]

        logger.info(f"Prepared {len(final_urls)} URLs for testing:")
        logger.info(f"  - Priority sites: {len(priority_sites)}")
        logger.info(f"  - Existing cache: {len(existing_cache)}")
        logger.info(f"  - New discoveries: {len(new_discoveries)}")
        logger.info(f"  - Final corpus: {len(final_urls)}")

        return final_urls

    async def worker_function(self, worker_id: int):
        """Individual worker function for processing URLs"""

        logger.info(f"Worker {worker_id} started")

        # Initialize hybrid crawler for this worker
        crawler = HybridCrawler(headless=True)

        # Worker statistics
        worker_stats = {
            'processed': 0,
            'successful': 0,
            'failed': 0,
            'start_time': time.time(),
            'total_time': 0,
            'avg_time_per_url': 0
        }

        try:
            while True:
                try:
                    # Get next URL from queue (with timeout)
                    url = self.work_queue.get(timeout=5)

                    if url is None:  # Shutdown signal
                        break

                    # Process URL
                    start_time = time.time()

                    logger.info(f"Worker {worker_id} processing: {url}")

                    # Use semaphore to limit concurrent browser instances
                    async with self.browser_semaphore:
                        result = await crawler.extract_content_hybrid(url)

                    processing_time = time.time() - start_time

                    # Add worker info to result
                    result['worker_id'] = worker_id
                    result['processing_time'] = processing_time

                    # Save to database
                    self.database.save_test_result(result, worker_id)

                    # Update worker stats
                    worker_stats['processed'] += 1
                    worker_stats['total_time'] += processing_time
                    worker_stats['avg_time_per_url'] = worker_stats['total_time'] / worker_stats['processed']

                    if result['status'] == 'PASS':
                        worker_stats['successful'] += 1
                    else:
                        worker_stats['failed'] += 1

                    # Update global progress
                    with self.progress_lock:
                        self.processed_urls += 1
                        if result['status'] == 'PASS':
                            self.successful_tests += 1
                        else:
                            self.failed_tests += 1

                        # Log progress every 10 URLs
                        if self.processed_urls % 10 == 0:
                            progress = (self.processed_urls / self.total_urls) * 100
                            success_rate = (self.successful_tests / self.processed_urls) * 100
                            logger.info(f"Progress: {self.processed_urls}/{self.total_urls} ({progress:.1f}%) "
                                      f"Success rate: {success_rate:.1f}%")

                    # Add to results queue
                    self.results_queue.put(result)

                    # Mark task as done
                    self.work_queue.task_done()

                    # Small delay to prevent overwhelming
                    await asyncio.sleep(0.1)

                except Empty:
                    # No more work available
                    logger.info(f"Worker {worker_id} - no more work available")
                    break

                except Exception as e:
                    logger.error(f"Worker {worker_id} error processing {url}: {str(e)}")

                    # Create error result
                    error_result = {
                        'url': url,
                        'status': 'ERROR',
                        'method': 'worker_error',
                        'error_message': str(e),
                        'worker_id': worker_id,
                        'timestamp': datetime.now().isoformat(),
                        'content_quality_score': 0
                    }

                    self.database.save_test_result(error_result, worker_id)
                    self.results_queue.put(error_result)

                    with self.progress_lock:
                        self.processed_urls += 1
                        self.failed_tests += 1

                    self.work_queue.task_done()

        finally:
            # Cleanup worker resources
            await crawler.cleanup()

            # Store worker stats
            with self.stats_lock:
                self.worker_stats[worker_id] = worker_stats

            logger.info(f"Worker {worker_id} completed: {worker_stats['processed']} URLs, "
                       f"{worker_stats['successful']} successful, "
                       f"avg {worker_stats['avg_time_per_url']:.2f}s per URL")

    async def monitor_progress(self):
        """Monitor and log progress periodically"""

        while True:
            try:
                await asyncio.sleep(30)  # Update every 30 seconds

                with self.progress_lock:
                    if self.total_urls > 0:
                        progress = (self.processed_urls / self.total_urls) * 100
                        success_rate = (self.successful_tests / max(1, self.processed_urls)) * 100
                        elapsed = time.time() - self.start_time

                        logger.info(f"=== Progress Update ===")
                        logger.info(f"Processed: {self.processed_urls}/{self.total_urls} ({progress:.1f}%)")
                        logger.info(f"Success rate: {success_rate:.1f}%")
                        logger.info(f"Elapsed time: {elapsed:.1f}s")

                        if self.processed_urls > 0:
                            avg_time = elapsed / self.processed_urls
                            remaining = self.total_urls - self.processed_urls
                            eta = remaining * avg_time
                            logger.info(f"ETA: {eta:.1f}s ({eta/60:.1f} minutes)")

                        logger.info("=" * 25)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.warning(f"Error in progress monitor: {str(e)}")

    def collect_results(self):
        """Collect all results from the results queue"""

        logger.info("Collecting test results...")

        while not self.results_queue.empty():
            try:
                result = self.results_queue.get_nowait()
                self.all_results.append(result)
            except Empty:
                break

        logger.info(f"Collected {len(self.all_results)} test results")

    def generate_final_summary(self) -> Dict[str, Any]:
        """Generate comprehensive final summary"""

        total_time = time.time() - self.start_time

        summary = {
            'test_session': {
                'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
                'end_time': datetime.now().isoformat(),
                'total_duration': total_time,
                'target_cache': self.target_cache,
                'num_workers': self.num_workers
            },

            'overall_metrics': {
                'total_tests': self.processed_urls,
                'successful_tests': self.successful_tests,
                'failed_tests': self.failed_tests,
                'overall_success_rate': self.successful_tests / max(1, self.processed_urls),
                'avg_time_per_test': total_time / max(1, self.processed_urls)
            },

            'worker_performance': self.worker_stats.copy(),

            'method_breakdown': {},
            'protection_breakdown': {},
            'quality_metrics': {
                'avg_quality_score': 0,
                'quality_distribution': {},
                'high_quality_sites': []
            }
        }

        # Analyze results by method and protection type
        method_stats = {}
        protection_stats = {}
        quality_scores = []

        for result in self.all_results:
            # Method analysis
            method = result.get('method', 'unknown')
            if method not in method_stats:
                method_stats[method] = {'count': 0, 'successes': 0, 'total_quality': 0}

            method_stats[method]['count'] += 1
            if result.get('status') == 'PASS':
                method_stats[method]['successes'] += 1
            method_stats[method]['total_quality'] += result.get('content_quality_score', 0)

            # Protection analysis
            protection = result.get('protection_type', 'unknown')
            if protection not in protection_stats:
                protection_stats[protection] = {'count': 0, 'successes': 0}

            protection_stats[protection]['count'] += 1
            if result.get('status') == 'PASS':
                protection_stats[protection]['successes'] += 1

            # Quality analysis
            quality = result.get('content_quality_score', 0)
            if quality > 0:
                quality_scores.append(quality)

                if quality >= 80:
                    summary['quality_metrics']['high_quality_sites'].append({
                        'url': result.get('url'),
                        'quality_score': quality,
                        'method': method
                    })

        # Calculate method breakdown
        for method, stats in method_stats.items():
            summary['method_breakdown'][method] = {
                'count': stats['count'],
                'success_rate': stats['successes'] / max(1, stats['count']),
                'avg_quality_score': stats['total_quality'] / max(1, stats['count'])
            }

        # Calculate protection breakdown
        for protection, stats in protection_stats.items():
            summary['protection_breakdown'][protection] = {
                'count': stats['count'],
                'success_rate': stats['successes'] / max(1, stats['count'])
            }

        # Calculate quality metrics
        if quality_scores:
            summary['quality_metrics']['avg_quality_score'] = sum(quality_scores) / len(quality_scores)

            # Quality distribution
            for score in quality_scores:
                if score >= 80:
                    category = 'excellent'
                elif score >= 60:
                    category = 'good'
                elif score >= 40:
                    category = 'fair'
                else:
                    category = 'poor'

                summary['quality_metrics']['quality_distribution'][category] = \
                    summary['quality_metrics']['quality_distribution'].get(category, 0) + 1

        return summary

    async def run_parallel_testing(self) -> Dict[str, Any]:
        """Main method to run parallel testing with 20 workers"""

        logger.info(f"Starting parallel testing with {self.num_workers} workers")
        logger.info(f"Target: {self.target_cache} websites")

        self.start_time = time.time()

        # Prepare test URLs
        test_urls = self.prepare_test_urls()
        self.total_urls = len(test_urls)

        # Add URLs to work queue
        for url in test_urls:
            self.work_queue.put(url)

        logger.info(f"Added {len(test_urls)} URLs to work queue")

        # Start workers
        tasks = []
        for worker_id in range(self.num_workers):
            task = asyncio.create_task(self.worker_function(worker_id))
            tasks.append(task)

        logger.info(f"Started {self.num_workers} workers")

        # Monitor progress
        monitor_task = asyncio.create_task(self.monitor_progress())

        # Wait for all work to complete
        await self.work_queue.join()

        # Signal workers to shutdown
        for _ in range(self.num_workers):
            self.work_queue.put(None)

        # Wait for all workers to complete
        await asyncio.gather(*tasks, return_exceptions=True)

        # Stop monitoring
        monitor_task.cancel()

        # Collect all results
        self.collect_results()

        # Generate final summary
        summary = self.generate_final_summary()

        total_time = time.time() - self.start_time
        logger.info(f"Parallel testing completed in {total_time:.1f} seconds")
        logger.info(f"Processed {self.processed_urls} URLs with {self.successful_tests} successes")

        return summary

    def save_summary_to_database(self, summary: Dict[str, Any]):
        """Save test session summary to database"""

        try:
            with self.database.db_lock:
                conn = sqlite3.connect(self.database.db_path)
                cursor = conn.cursor()

                # Insert performance benchmark
                cursor.execute("""
                    INSERT INTO performance_benchmarks (
                        test_session, test_date, total_tests, overall_success_rate,
                        avg_execution_time, avg_quality_score, crawl4ai_tests, crawl4ai_successes,
                        crawl4ai_avg_time, crawl4ai_avg_quality, playwright_tests, playwright_successes,
                        playwright_avg_time, playwright_avg_quality, cloudflare_tests, cloudflare_successes,
                        worker_count, browser_type, headless_mode
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    summary['test_session']['start_time'],
                    summary['test_session']['end_time'],
                    summary['overall_metrics']['total_tests'],
                    summary['overall_metrics']['overall_success_rate'],
                    summary['overall_metrics']['avg_time_per_test'],
                    summary['quality_metrics']['avg_quality_score'],
                    summary['method_breakdown'].get('crawl4ai', {}).get('count', 0),
                    summary['method_breakdown'].get('crawl4ai', {}).get('count', 0) *
                    summary['method_breakdown'].get('crawl4ai', {}).get('success_rate', 0),
                    0,  # crawl4ai_avg_time - would need to calculate separately
                    summary['method_breakdown'].get('crawl4ai', {}).get('avg_quality_score', 0),
                    summary['method_breakdown'].get('playwright', {}).get('count', 0),
                    summary['method_breakdown'].get('playwright', {}).get('count', 0) *
                    summary['method_breakdown'].get('playwright', {}).get('success_rate', 0),
                    0,  # playwright_avg_time - would need to calculate separately
                    summary['method_breakdown'].get('playwright', {}).get('avg_quality_score', 0),
                    summary['protection_breakdown'].get('cloudflare', {}).get('count', 0),
                    summary['protection_breakdown'].get('cloudflare', {}).get('count', 0) *
                    summary['protection_breakdown'].get('cloudflare', {}).get('success_rate', 0),
                    self.num_workers,
                    'chromium',  # Default browser type
                    True  # Headless mode
                ))

                conn.commit()
                conn.close()

                logger.info("Test summary saved to database")

        except Exception as e:
            logger.error(f"Error saving summary to database: {str(e)}")

# Test function
async def test_parallel_framework():
    """Test the parallel testing framework"""

    logger.info("=== Testing Parallel Framework ===")

    # Create framework with fewer workers for testing
    framework = ParallelTestingFramework(num_workers=5, target_cache=20)

    try:
        # Run parallel testing
        summary = await framework.run_parallel_testing()

        # Save summary to database
        framework.save_summary_to_database(summary)

        # Print results
        print(f"\n=== Test Results ===")
        print(f"Total tests: {summary['overall_metrics']['total_tests']}")
        print(f"Success rate: {summary['overall_metrics']['overall_success_rate']:.1%}")
        print(f"Average quality: {summary['quality_metrics']['avg_quality_score']:.1f}/100")
        print(f"Duration: {summary['test_session']['total_duration']:.1f}s")

        print(f"\nMethod breakdown:")
        for method, stats in summary['method_breakdown'].items():
            print(f"  {method}: {stats['count']} tests, {stats['success_rate']:.1%} success")

        print(f"\nProtection breakdown:")
        for protection, stats in summary['protection_breakdown'].items():
            print(f"  {protection}: {stats['count']} tests, {stats['success_rate']:.1%} success")

        return summary

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return None

if __name__ == "__main__":
    # Import required for database operations
    import sqlite3

    # Run test
    asyncio.run(test_parallel_framework())
