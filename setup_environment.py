#!/usr/bin/env python3
"""
Environment Setup Script for Playwright Integration
Creates virtual environment and installs all required dependencies
"""

import subprocess
import sys
import os
from pathlib import Path
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    
    logger.info(f"Running: {command}")
    if description:
        logger.info(f"Description: {description}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Command failed: {e}")
        if e.stderr:
            logger.error(f"Error: {e.stderr.strip()}")
        return False

def setup_virtual_environment():
    """Set up Python virtual environment for the project"""
    
    logger.info("=== Setting up Virtual Environment ===")
    
    venv_path = Path("venv")
    
    # Create virtual environment
    if not venv_path.exists():
        logger.info("Creating virtual environment...")
        if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
            return False
    else:
        logger.info("Virtual environment already exists")
    
    # Determine activation script path
    if os.name == 'nt':  # Windows
        activate_script = venv_path / "Scripts" / "activate"
        pip_path = venv_path / "Scripts" / "pip"
        python_path = venv_path / "Scripts" / "python"
    else:  # Unix/Linux/macOS
        activate_script = venv_path / "bin" / "activate"
        pip_path = venv_path / "bin" / "pip"
        python_path = venv_path / "bin" / "python"
    
    logger.info(f"Virtual environment created at: {venv_path.absolute()}")
    logger.info(f"Activation script: {activate_script}")
    logger.info(f"Python executable: {python_path}")
    
    return True, python_path, pip_path

def install_dependencies(pip_path):
    """Install all required Python packages"""
    
    logger.info("=== Installing Dependencies ===")
    
    # Core dependencies
    dependencies = [
        "playwright>=1.40.0",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0",
        "aiohttp>=3.9.0",
        "asyncio",
        "sqlite3",  # Usually built-in
        "pathlib",  # Usually built-in
        "urllib3>=2.0.0",
        "certifi>=2023.0.0",
        "charset-normalizer>=3.3.0",
        "idna>=3.4"
    ]
    
    # Install each dependency
    for dep in dependencies:
        logger.info(f"Installing {dep}...")
        if not run_command(f"{pip_path} install {dep}", f"Installing {dep}"):
            logger.warning(f"Failed to install {dep}, continuing...")
    
    # Install Playwright browsers
    logger.info("Installing Playwright browsers...")
    playwright_install_cmd = f"{pip_path} install playwright && {python_path} -m playwright install"
    if not run_command(playwright_install_cmd, "Installing Playwright browsers"):
        logger.error("Failed to install Playwright browsers")
        return False
    
    return True

def create_requirements_file():
    """Create requirements.txt file for the project"""
    
    logger.info("=== Creating requirements.txt ===")
    
    requirements_content = """# Playwright Integration Requirements
playwright>=1.40.0
requests>=2.31.0
beautifulsoup4>=4.12.0
lxml>=4.9.0
aiohttp>=3.9.0
urllib3>=2.0.0
certifi>=2023.0.0
charset-normalizer>=3.3.0
idna>=3.4

# Development dependencies
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
"""
    
    with open("requirements.txt", "w") as f:
        f.write(requirements_content)
    
    logger.info("Created requirements.txt")
    return True

def create_activation_script():
    """Create convenient activation script"""
    
    logger.info("=== Creating Activation Scripts ===")
    
    # Unix/Linux/macOS activation script
    unix_script = """#!/bin/bash
# Playwright Integration Environment Activation Script

echo "Activating Playwright Integration virtual environment..."

# Activate virtual environment
source venv/bin/activate

# Set environment variables
export PLAYWRIGHT_BROWSERS_PATH=venv/lib/python*/site-packages/playwright/driver/
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

echo "Environment activated!"
echo "Python: $(which python)"
echo "Pip: $(which pip)"
echo ""
echo "Available commands:"
echo "  python test_runner.py          - Run full test suite"
echo "  python google_search_api.py    - Test Google Search API"
echo "  python playwright_tester.py    - Test Playwright framework"
echo "  python hybrid_crawler.py       - Test hybrid crawler"
echo "  deactivate                     - Exit virtual environment"
echo ""
"""
    
    with open("activate_env.sh", "w") as f:
        f.write(unix_script)
    
    # Make executable
    os.chmod("activate_env.sh", 0o755)
    
    # Windows activation script
    windows_script = """@echo off
REM Playwright Integration Environment Activation Script

echo Activating Playwright Integration virtual environment...

REM Activate virtual environment
call venv\\Scripts\\activate.bat

REM Set environment variables
set PYTHONPATH=%PYTHONPATH%;%CD%

echo Environment activated!
echo Python: %CD%\\venv\\Scripts\\python.exe
echo Pip: %CD%\\venv\\Scripts\\pip.exe
echo.
echo Available commands:
echo   python test_runner.py          - Run full test suite
echo   python google_search_api.py    - Test Google Search API
echo   python playwright_tester.py    - Test Playwright framework
echo   python hybrid_crawler.py       - Test hybrid crawler
echo   deactivate                     - Exit virtual environment
echo.
"""
    
    with open("activate_env.bat", "w") as f:
        f.write(windows_script)
    
    logger.info("Created activation scripts: activate_env.sh (Unix) and activate_env.bat (Windows)")
    return True

def create_project_structure():
    """Create organized project directory structure"""
    
    logger.info("=== Creating Project Structure ===")
    
    directories = [
        "screenshots",
        "reports", 
        "data",
        "logs",
        "tests",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"Created directory: {directory}")
    
    # Create .gitignore
    gitignore_content = """# Virtual Environment
venv/
env/
.env

# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# Playwright
screenshots/
test-results/
playwright-report/

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Reports
reports/*.html
reports/*.json
reports/*.md

# Temporary files
temp/
tmp/
*.tmp
"""
    
    with open(".gitignore", "w") as f:
        f.write(gitignore_content)
    
    logger.info("Created .gitignore file")
    return True

def verify_installation(python_path):
    """Verify that all components are properly installed"""
    
    logger.info("=== Verifying Installation ===")
    
    # Test Python
    if not run_command(f"{python_path} --version", "Checking Python version"):
        return False
    
    # Test Playwright
    test_script = """
import sys
try:
    from playwright.async_api import async_playwright
    print("✅ Playwright imported successfully")
    
    import requests
    print("✅ Requests imported successfully")
    
    import asyncio
    print("✅ Asyncio imported successfully")
    
    import sqlite3
    print("✅ SQLite3 imported successfully")
    
    print("✅ All core dependencies verified")
    sys.exit(0)
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
"""
    
    with open("verify_install.py", "w") as f:
        f.write(test_script)
    
    success = run_command(f"{python_path} verify_install.py", "Verifying package imports")
    
    # Clean up
    os.remove("verify_install.py")
    
    return success

def main():
    """Main setup function"""
    
    logger.info("🚀 Starting Playwright Integration Environment Setup")
    logger.info("=" * 60)
    
    try:
        # Step 1: Create virtual environment
        success, python_path, pip_path = setup_virtual_environment()
        if not success:
            logger.error("Failed to create virtual environment")
            return False
        
        # Step 2: Install dependencies
        if not install_dependencies(pip_path):
            logger.error("Failed to install dependencies")
            return False
        
        # Step 3: Create project files
        create_requirements_file()
        create_activation_script()
        create_project_structure()
        
        # Step 4: Verify installation
        if not verify_installation(python_path):
            logger.error("Installation verification failed")
            return False
        
        # Success message
        logger.info("=" * 60)
        logger.info("✅ Environment setup completed successfully!")
        logger.info("")
        logger.info("Next steps:")
        logger.info("1. Activate environment:")
        logger.info("   Unix/Linux/macOS: source activate_env.sh")
        logger.info("   Windows: activate_env.bat")
        logger.info("")
        logger.info("2. Run tests:")
        logger.info("   python test_runner.py")
        logger.info("")
        logger.info("3. Individual component tests:")
        logger.info("   python google_search_api.py")
        logger.info("   python playwright_tester.py")
        logger.info("   python hybrid_crawler.py")
        logger.info("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"Setup failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
