#!/usr/bin/env python3
"""
Playwright Testing Framework for Protected Website Analysis
Complements existing crawl4ai system with browser automation capabilities
"""

import asyncio
import time
import json
import os
from datetime import datetime
from typing import Dict, List, Optional, Any
from urllib.parse import urlparse, urljoin
import logging
from pathlib import Path

# Playwright imports
try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
except ImportError:
    print("Playwright not installed. Run: pip install playwright")
    print("Then run: playwright install")
    exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PlaywrightTester:
    """Advanced browser automation testing for protected websites"""
    
    def __init__(self, headless: bool = True, browser_type: str = "chromium"):
        self.headless = headless
        self.browser_type = browser_type  # chromium, firefox, webkit
        self.browser = None
        self.context = None
        
        # Configuration
        self.timeout = 30000  # 30 seconds
        self.wait_for_load = "networkidle"  # or "domcontentloaded"
        self.viewport = {"width": 1920, "height": 1080}
        
        # User agents for different browsers
        self.user_agents = {
            "chromium": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "firefox": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:120.0) Gecko/20100101 Firefox/120.0",
            "webkit": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        }
        
        # Results storage
        self.screenshots_dir = Path("screenshots")
        self.screenshots_dir.mkdir(exist_ok=True)
        
        # Metrics tracking
        self.test_results = []
        
    async def initialize_browser(self):
        """Initialize Playwright browser with optimal settings"""
        
        logger.info(f"Initializing {self.browser_type} browser (headless: {self.headless})")
        
        self.playwright = await async_playwright().start()
        
        # Launch browser based on type
        if self.browser_type == "chromium":
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=[
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--disable-web-security",
                    "--disable-features=VizDisplayCompositor"
                ]
            )
        elif self.browser_type == "firefox":
            self.browser = await self.playwright.firefox.launch(headless=self.headless)
        elif self.browser_type == "webkit":
            self.browser = await self.playwright.webkit.launch(headless=self.headless)
        else:
            raise ValueError(f"Unsupported browser type: {self.browser_type}")
        
        # Create browser context with realistic settings
        self.context = await self.browser.new_context(
            viewport=self.viewport,
            user_agent=self.user_agents[self.browser_type],
            java_script_enabled=True,
            accept_downloads=False,
            ignore_https_errors=True
        )
        
        logger.info("Browser initialized successfully")
    
    async def extract_content_with_browser(self, url: str) -> Dict[str, Any]:
        """Extract content using browser automation with comprehensive metrics"""
        
        start_time = time.time()
        result = {
            'url': url,
            'status': 'FAIL',
            'method': 'playwright',
            'browser_engine': self.browser_type,
            'timestamp': datetime.now().isoformat(),
            'execution_time': 0,
            'error_message': '',
            
            # Basic content metrics (matching crawl4ai format)
            'title': '',
            'meta_description': '',
            'word_count': 0,
            'paragraphs': 0,
            'headings': {'h1': 0, 'h2': 0, 'h3': 0, 'h4': 0, 'h5': 0, 'h6': 0},
            'links': {'total': 0, 'internal': 0, 'external': 0},
            'images': {'total': 0, 'with_alt': 0, 'without_alt': 0},
            'language': 'unknown',
            'content_quality_score': 0,
            
            # Playwright-specific metrics
            'javascript_errors': [],
            'network_requests': 0,
            'load_time': 0,
            'screenshot_path': '',
            'protection_bypassed': False,
            'protection_type': 'unknown',
            'spa_detected': False,
            'ajax_content': 0,
            'modal_dialogs': 0,
            
            # Technology detection
            'technology_stack': [],
            'frameworks_detected': [],
            
            # Social media and contact info
            'social_media': [],
            'emails': [],
            'phone_numbers': [],
            
            # SEO metrics
            'structured_data': False,
            'readability_score': 0
        }
        
        try:
            if not self.browser:
                await self.initialize_browser()
            
            # Create new page
            page = await self.context.new_page()
            
            # Set up error monitoring
            js_errors = []
            page.on("pageerror", lambda error: js_errors.append(str(error)))
            page.on("console", lambda msg: 
                js_errors.append(f"Console {msg.type}: {msg.text}") 
                if msg.type in ["error", "warning"]
            )
            
            # Monitor network requests
            network_requests = []
            page.on("request", lambda request: network_requests.append(request.url))
            
            logger.info(f"Testing URL: {url}")
            
            # Navigate to page with timeout
            try:
                response = await page.goto(
                    url, 
                    wait_until=self.wait_for_load,
                    timeout=self.timeout
                )
                
                if response:
                    result['status_code'] = response.status
                    result['protection_bypassed'] = response.status == 200
                
            except Exception as nav_error:
                result['error_message'] = f"Navigation failed: {str(nav_error)}"
                logger.warning(f"Navigation failed for {url}: {str(nav_error)}")
                return result
            
            # Wait for additional content to load
            await asyncio.sleep(2)
            
            # Extract content metrics
            await self._extract_basic_content(page, result)
            await self._extract_structure_metrics(page, result)
            await self._extract_technology_stack(page, result)
            await self._extract_social_and_contact(page, result)
            await self._detect_protection_systems(page, result)
            
            # Capture screenshot
            screenshot_path = await self._capture_screenshot(page, url)
            result['screenshot_path'] = screenshot_path
            
            # Store Playwright-specific metrics
            result['javascript_errors'] = js_errors[:10]  # Limit to first 10 errors
            result['network_requests'] = len(network_requests)
            result['load_time'] = time.time() - start_time
            
            # Calculate content quality score
            result['content_quality_score'] = self._calculate_quality_score(result)
            
            # Mark as successful if we got content
            if result['word_count'] > 0 or result['title']:
                result['status'] = 'PASS'
                result['protection_bypassed'] = True
            
            await page.close()
            
        except Exception as e:
            result['error_message'] = str(e)
            logger.error(f"Error testing {url}: {str(e)}")
        
        finally:
            result['execution_time'] = time.time() - start_time
        
        return result
    
    async def _extract_basic_content(self, page: Page, result: Dict):
        """Extract basic content metrics"""
        
        try:
            # Title
            title = await page.title()
            result['title'] = title.strip() if title else ''
            
            # Meta description
            meta_desc = await page.get_attribute('meta[name="description"]', 'content')
            result['meta_description'] = meta_desc.strip() if meta_desc else ''
            
            # Language
            lang = await page.get_attribute('html', 'lang')
            result['language'] = lang if lang else 'unknown'
            
            # Text content
            text_content = await page.inner_text('body')
            if text_content:
                words = text_content.split()
                result['word_count'] = len(words)
                
                # Count paragraphs
                paragraphs = await page.locator('p').count()
                result['paragraphs'] = paragraphs
            
        except Exception as e:
            logger.warning(f"Error extracting basic content: {str(e)}")
    
    async def _extract_structure_metrics(self, page: Page, result: Dict):
        """Extract structural metrics (headings, links, images)"""
        
        try:
            # Headings
            for level in range(1, 7):
                count = await page.locator(f'h{level}').count()
                result['headings'][f'h{level}'] = count
            
            # Links
            all_links = await page.locator('a[href]').count()
            result['links']['total'] = all_links
            
            # Images
            all_images = await page.locator('img').count()
            images_with_alt = await page.locator('img[alt]').count()
            result['images']['total'] = all_images
            result['images']['with_alt'] = images_with_alt
            result['images']['without_alt'] = all_images - images_with_alt
            
        except Exception as e:
            logger.warning(f"Error extracting structure metrics: {str(e)}")
    
    async def _extract_technology_stack(self, page: Page, result: Dict):
        """Detect technology stack and frameworks"""
        
        try:
            # Check for common frameworks in page source
            page_content = await page.content()
            
            frameworks = {
                'React': ['react', '_react', 'ReactDOM'],
                'Vue.js': ['vue.js', '__vue__', 'Vue'],
                'Angular': ['ng-', 'angular', '_angular'],
                'jQuery': ['jquery', '$', 'jQuery'],
                'Bootstrap': ['bootstrap', 'btn-', 'container-fluid'],
                'WordPress': ['wp-content', 'wordpress', 'wp-includes'],
                'Shopify': ['shopify', 'cdn.shopify'],
                'Cloudflare': ['cloudflare', '__cf_bm', 'cf-ray']
            }
            
            detected = []
            for framework, indicators in frameworks.items():
                if any(indicator.lower() in page_content.lower() for indicator in indicators):
                    detected.append(framework)
            
            result['technology_stack'] = detected
            result['frameworks_detected'] = detected
            
        except Exception as e:
            logger.warning(f"Error detecting technology stack: {str(e)}")
    
    async def _extract_social_and_contact(self, page: Page, result: Dict):
        """Extract social media links and contact information"""
        
        try:
            # Social media platforms
            social_platforms = {
                'Facebook': ['facebook.com', 'fb.com'],
                'Twitter': ['twitter.com', 'x.com'],
                'Instagram': ['instagram.com'],
                'LinkedIn': ['linkedin.com'],
                'YouTube': ['youtube.com', 'youtu.be'],
                'TikTok': ['tiktok.com']
            }
            
            page_content = await page.content()
            detected_social = []
            
            for platform, domains in social_platforms.items():
                if any(domain in page_content.lower() for domain in domains):
                    detected_social.append(platform)
            
            result['social_media'] = detected_social
            
        except Exception as e:
            logger.warning(f"Error extracting social/contact info: {str(e)}")
    
    async def _detect_protection_systems(self, page: Page, result: Dict):
        """Detect protection systems and challenges"""
        
        try:
            page_content = await page.content().lower()
            
            # Cloudflare detection
            if any(indicator in page_content for indicator in [
                'cloudflare', 'checking your browser', 'ddos protection',
                'cf-ray', '__cf_bm', 'challenge-platform'
            ]):
                result['protection_type'] = 'cloudflare'
            
            # Akamai detection
            elif any(indicator in page_content for indicator in [
                'akamai', 'akamai technologies', 'reference #'
            ]):
                result['protection_type'] = 'akamai'
            
            # CAPTCHA detection
            elif any(indicator in page_content for indicator in [
                'captcha', 'recaptcha', 'hcaptcha', 'verify you are human'
            ]):
                result['protection_type'] = 'captcha'
            
            # Rate limiting
            elif any(indicator in page_content for indicator in [
                'rate limit', 'too many requests', '429', 'slow down'
            ]):
                result['protection_type'] = 'rate_limited'
            
            else:
                result['protection_type'] = 'none_detected'
            
        except Exception as e:
            logger.warning(f"Error detecting protection systems: {str(e)}")
    
    async def _capture_screenshot(self, page: Page, url: str) -> str:
        """Capture screenshot for visual verification"""
        
        try:
            # Create safe filename from URL
            domain = urlparse(url).netloc.replace('www.', '')
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{domain}_{timestamp}.png"
            filepath = self.screenshots_dir / filename
            
            await page.screenshot(path=str(filepath), full_page=True)
            return str(filepath)
            
        except Exception as e:
            logger.warning(f"Error capturing screenshot: {str(e)}")
            return ""
    
    def _calculate_quality_score(self, result: Dict) -> int:
        """Calculate content quality score (matching crawl4ai methodology)"""
        
        score = 0
        
        # Basic content (40 points)
        if result.get('title'): score += 10
        if result.get('meta_description'): score += 10
        
        word_count = result.get('word_count', 0)
        if word_count > 1000: score += 20
        elif word_count > 500: score += 15
        elif word_count > 100: score += 10
        
        # Structure quality (30 points)
        headings_total = sum(result.get('headings', {}).values())
        paragraphs = result.get('paragraphs', 0)
        if headings_total > 3 and paragraphs > 5: score += 15
        elif headings_total > 0 and paragraphs > 0: score += 10
        
        links_total = result.get('links', {}).get('total', 0)
        if links_total > 10: score += 10
        elif links_total > 0: score += 5
        
        # Rich content (20 points)
        if result.get('images', {}).get('total', 0) > 0: score += 5
        if result.get('technology_stack'): score += 5
        if result.get('social_media'): score += 5
        if result.get('protection_bypassed'): score += 5
        
        # Technical quality (10 points)
        if result.get('language') != 'unknown': score += 5
        if len(result.get('javascript_errors', [])) == 0: score += 5
        
        return min(100, score)
    
    async def close(self):
        """Clean up browser resources"""
        
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()

# Test function
async def test_playwright_framework():
    """Test the Playwright framework with priority sites"""
    
    tester = PlaywrightTester(headless=True, browser_type="chromium")
    
    # Test priority Cloudflare sites
    test_sites = [
        "https://canva.com",
        "https://udemy.com", 
        "https://kickstarter.com"
    ]
    
    results = []
    
    try:
        for url in test_sites:
            print(f"\nTesting: {url}")
            result = await tester.extract_content_with_browser(url)
            results.append(result)
            
            print(f"Status: {result['status']}")
            print(f"Protection bypassed: {result['protection_bypassed']}")
            print(f"Quality score: {result['content_quality_score']}/100")
            print(f"Word count: {result['word_count']}")
            print(f"Load time: {result['load_time']:.2f}s")
    
    finally:
        await tester.close()
    
    return results

if __name__ == "__main__":
    # Run test
    asyncio.run(test_playwright_framework())
