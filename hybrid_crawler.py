#!/usr/bin/env python3
"""
Hybrid Crawling Framework - Combines Crawl4AI and Playwright
Intelligent routing system for optimal extraction method selection
"""

import asyncio
import time
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# Import our components
from google_search_api import CustomSearchAPI
from playwright_tester import PlaywrightTester
from protection_detector import ProtectionDetector

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedContentExtractor:
    """Mock of the existing crawl4ai extractor for integration"""
    
    def __init__(self):
        self.user_agents = [
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        self.max_retries = 3
        self.retry_delays = [2, 5, 10]
    
    def extract_with_fallback(self, url: str) -> Dict[str, Any]:
        """Mock crawl4ai extraction with fallback mechanism"""
        
        start_time = time.time()
        
        # Simulate crawl4ai extraction
        result = {
            'url': url,
            'status': 'PASS',
            'method': 'crawl4ai',
            'timestamp': datetime.now().isoformat(),
            'execution_time': 0,
            'error_message': '',
            
            # Basic metrics
            'title': f'Mock Title for {url}',
            'meta_description': 'Mock meta description',
            'word_count': 500,
            'paragraphs': 10,
            'headings': {'h1': 1, 'h2': 3, 'h3': 2, 'h4': 0, 'h5': 0, 'h6': 0},
            'links': {'total': 25, 'internal': 20, 'external': 5},
            'images': {'total': 5, 'with_alt': 3, 'without_alt': 2},
            'language': 'en',
            'content_quality_score': 65,
            
            # Technology detection
            'technology_stack': ['Bootstrap', 'jQuery'],
            'social_media': ['Facebook', 'Twitter'],
            'emails': [],
            'phone_numbers': [],
            'structured_data': False,
            'readability_score': 70
        }
        
        # Simulate known failures for Cloudflare sites
        cloudflare_sites = ['canva.com', 'udemy.com', 'kickstarter.com', 'technewsworld.com', 'cruisecritic.com']
        if any(site in url.lower() for site in cloudflare_sites):
            result['status'] = 'FAIL'
            result['error_message'] = 'HTTP 403: Checking your browser before accessing'
            result['content_quality_score'] = 0
            result['word_count'] = 0
        
        result['execution_time'] = time.time() - start_time
        return result

class HybridCrawler:
    """Unified crawling framework combining crawl4ai and Playwright"""
    
    def __init__(self, headless: bool = True):
        # Initialize components
        self.crawl4ai_extractor = EnhancedContentExtractor()
        self.playwright_tester = None  # Initialize when needed
        self.protection_detector = ProtectionDetector()
        self.search_api = CustomSearchAPI()
        
        # Configuration
        self.headless = headless
        self.max_concurrent_browsers = 5  # Limit browser instances
        self.active_browsers = 0
        
        # Statistics
        self.stats = {
            'total_tests': 0,
            'crawl4ai_success': 0,
            'playwright_success': 0,
            'total_failures': 0,
            'method_usage': {'crawl4ai': 0, 'playwright': 0},
            'protection_types': {},
            'start_time': time.time()
        }
        
        # Results storage
        self.test_results = []
    
    async def initialize_playwright(self):
        """Initialize Playwright when needed"""
        if not self.playwright_tester:
            self.playwright_tester = PlaywrightTester(headless=self.headless)
            await self.playwright_tester.initialize_browser()
            logger.info("Playwright browser initialized")
    
    async def extract_content_hybrid(self, url: str) -> Dict[str, Any]:
        """Main hybrid extraction method with intelligent routing"""
        
        logger.info(f"Processing URL: {url}")
        self.stats['total_tests'] += 1
        
        # Step 1: Detect protection and get strategy
        strategy = self.protection_detector.get_extraction_strategy(url)
        detection = strategy['detection_result']
        
        logger.info(f"Protection detected: {detection['protection_type']} "
                   f"(confidence: {detection['confidence']:.2f})")
        logger.info(f"Strategy: {strategy['primary_method']} -> {strategy.get('fallback_method', 'none')}")
        
        # Track protection types
        protection_type = detection['protection_type']
        self.stats['protection_types'][protection_type] = \
            self.stats['protection_types'].get(protection_type, 0) + 1
        
        result = None
        
        # Step 2: Try primary method
        primary_method = strategy['primary_method']
        
        if primary_method == 'crawl4ai':
            logger.info("Trying crawl4ai (primary)")
            self.stats['method_usage']['crawl4ai'] += 1
            result = self.crawl4ai_extractor.extract_with_fallback(url)
            
            # Check if crawl4ai succeeded
            if result['status'] == 'PASS' and result['word_count'] > 0:
                logger.info(f"✅ Crawl4ai succeeded: {result['content_quality_score']}/100 quality")
                self.stats['crawl4ai_success'] += 1
                return result
            else:
                logger.warning(f"❌ Crawl4ai failed: {result.get('error_message', 'Unknown error')}")
        
        elif primary_method == 'playwright':
            logger.info("Trying Playwright (primary)")
            await self.initialize_playwright()
            self.stats['method_usage']['playwright'] += 1
            result = await self.playwright_tester.extract_content_with_browser(url)
            
            if result['status'] == 'PASS' and result['word_count'] > 0:
                logger.info(f"✅ Playwright succeeded: {result['content_quality_score']}/100 quality")
                self.stats['playwright_success'] += 1
                return result
            else:
                logger.warning(f"❌ Playwright failed: {result.get('error_message', 'Unknown error')}")
        
        # Step 3: Try fallback method if primary failed
        fallback_method = strategy.get('fallback_method')
        
        if fallback_method and fallback_method != primary_method:
            logger.info(f"Trying {fallback_method} (fallback)")
            
            if fallback_method == 'playwright':
                await self.initialize_playwright()
                self.stats['method_usage']['playwright'] += 1
                result = await self.playwright_tester.extract_content_with_browser(url)
                
                if result['status'] == 'PASS' and result['word_count'] > 0:
                    logger.info(f"✅ Playwright fallback succeeded: {result['content_quality_score']}/100 quality")
                    self.stats['playwright_success'] += 1
                    return result
                else:
                    logger.warning(f"❌ Playwright fallback failed: {result.get('error_message', 'Unknown error')}")
            
            elif fallback_method == 'crawl4ai':
                self.stats['method_usage']['crawl4ai'] += 1
                result = self.crawl4ai_extractor.extract_with_fallback(url)
                
                if result['status'] == 'PASS' and result['word_count'] > 0:
                    logger.info(f"✅ Crawl4ai fallback succeeded: {result['content_quality_score']}/100 quality")
                    self.stats['crawl4ai_success'] += 1
                    return result
                else:
                    logger.warning(f"❌ Crawl4ai fallback failed: {result.get('error_message', 'Unknown error')}")
        
        # Step 4: Both methods failed
        logger.error(f"❌ All methods failed for {url}")
        self.stats['total_failures'] += 1
        
        # Return the last result with failure status
        if result:
            result['status'] = 'FAIL'
            result['final_method'] = 'hybrid_failed'
        else:
            result = {
                'url': url,
                'status': 'FAIL',
                'method': 'hybrid_failed',
                'error_message': 'All extraction methods failed',
                'content_quality_score': 0,
                'word_count': 0
            }
        
        return result
    
    async def test_priority_sites(self) -> List[Dict]:
        """Test the 5 priority Cloudflare sites that currently fail"""
        
        priority_sites = [
            "https://canva.com",
            "https://udemy.com",
            "https://kickstarter.com", 
            "https://technewsworld.com",
            "https://cruisecritic.com"
        ]
        
        logger.info(f"Testing {len(priority_sites)} priority Cloudflare sites...")
        
        results = []
        for url in priority_sites:
            try:
                result = await self.extract_content_hybrid(url)
                results.append(result)
                
                # Add to overall results
                self.test_results.append(result)
                
                # Small delay between tests
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Error testing {url}: {str(e)}")
                error_result = {
                    'url': url,
                    'status': 'ERROR',
                    'error_message': str(e),
                    'content_quality_score': 0
                }
                results.append(error_result)
                self.test_results.append(error_result)
        
        return results
    
    async def test_discovered_sites(self, max_sites: int = 50) -> List[Dict]:
        """Test newly discovered sites from Google Search API"""
        
        logger.info("Discovering new protected sites via Google Search API...")
        
        # Get test corpus from Google Search API
        corpus = self.search_api.prepare_test_corpus(max_new_sites=max_sites)
        new_sites = corpus['new_discoveries'][:max_sites]
        
        logger.info(f"Testing {len(new_sites)} newly discovered sites...")
        
        results = []
        for i, domain in enumerate(new_sites):
            try:
                # Convert domain to full URL
                url = f"https://{domain}"
                
                result = await self.extract_content_hybrid(url)
                results.append(result)
                
                # Add to overall results
                self.test_results.append(result)
                
                logger.info(f"Completed {i+1}/{len(new_sites)}: {domain}")
                
                # Small delay between tests
                await asyncio.sleep(0.5)
                
            except Exception as e:
                logger.error(f"Error testing {domain}: {str(e)}")
                error_result = {
                    'url': f"https://{domain}",
                    'status': 'ERROR',
                    'error_message': str(e),
                    'content_quality_score': 0
                }
                results.append(error_result)
                self.test_results.append(error_result)
        
        return results
    
    def get_performance_summary(self) -> Dict:
        """Generate performance summary statistics"""
        
        total_time = time.time() - self.stats['start_time']
        
        summary = {
            'total_tests': self.stats['total_tests'],
            'total_time': total_time,
            'avg_time_per_test': total_time / max(1, self.stats['total_tests']),
            
            # Success rates
            'overall_success_rate': (self.stats['crawl4ai_success'] + self.stats['playwright_success']) / max(1, self.stats['total_tests']),
            'crawl4ai_success_rate': self.stats['crawl4ai_success'] / max(1, self.stats['method_usage']['crawl4ai']),
            'playwright_success_rate': self.stats['playwright_success'] / max(1, self.stats['method_usage']['playwright']),
            
            # Method usage
            'method_usage': self.stats['method_usage'].copy(),
            'protection_types': self.stats['protection_types'].copy(),
            
            # Quality scores
            'quality_scores': [r.get('content_quality_score', 0) for r in self.test_results if r.get('status') == 'PASS'],
            'avg_quality_score': 0
        }
        
        if summary['quality_scores']:
            summary['avg_quality_score'] = sum(summary['quality_scores']) / len(summary['quality_scores'])
        
        return summary
    
    async def cleanup(self):
        """Clean up resources"""
        if self.playwright_tester:
            await self.playwright_tester.close()
            logger.info("Playwright browser closed")

# Test function
async def test_hybrid_crawler():
    """Test the hybrid crawler with priority sites"""
    
    crawler = HybridCrawler(headless=True)
    
    try:
        # Test priority sites
        logger.info("=== Testing Priority Cloudflare Sites ===")
        priority_results = await crawler.test_priority_sites()
        
        # Test some discovered sites
        logger.info("\n=== Testing Discovered Sites ===")
        discovered_results = await crawler.test_discovered_sites(max_sites=10)
        
        # Generate summary
        summary = crawler.get_performance_summary()
        
        print(f"\n=== Hybrid Crawler Test Results ===")
        print(f"Total tests: {summary['total_tests']}")
        print(f"Overall success rate: {summary['overall_success_rate']:.1%}")
        print(f"Crawl4ai success rate: {summary['crawl4ai_success_rate']:.1%}")
        print(f"Playwright success rate: {summary['playwright_success_rate']:.1%}")
        print(f"Average quality score: {summary['avg_quality_score']:.1f}/100")
        print(f"Total time: {summary['total_time']:.1f}s")
        
        print(f"\nMethod usage:")
        for method, count in summary['method_usage'].items():
            print(f"  {method}: {count}")
        
        print(f"\nProtection types detected:")
        for ptype, count in summary['protection_types'].items():
            print(f"  {ptype}: {count}")
    
    finally:
        await crawler.cleanup()

if __name__ == "__main__":
    asyncio.run(test_hybrid_crawler())
