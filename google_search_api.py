#!/usr/bin/env python3
"""
Google Custom Search API Integration for Playwright Testing Framework
Discovers protected websites to expand test corpus beyond current 132 cached sites
"""

import requests
import time
import random
import re
from urllib.parse import urlparse
from typing import List, Dict, Set
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CustomSearchAPI:
    """Google Custom Search API integration for discovering protected websites"""
    
    def __init__(self):
        # Provided credentials
        self.api_key = "AIzaSyCwTnS_5HI1A2aPgMkj8tvWrluE75a61pc"
        self.search_engine_id = "168d32a5eb0374b7e"
        self.base_url = "https://www.googleapis.com/customsearch/v1"
        
        # Search queries from playwright_test_plan.md
        self.search_queries = [
            "site protected by cloudflare",
            "site using akamai protection", 
            "website with captcha protection",
            "sites with anti-bot measures"
        ]
        
        # Rate limiting
        self.requests_per_day = 100  # Google API limit
        self.delay_between_requests = 1.0  # Respectful delay
        
        # Results storage
        self.discovered_domains = set()
        self.search_results = []
        
    def execute_search(self, query: str, start_index: int = 1, num_results: int = 10) -> Dict:
        """Execute a single Google Custom Search API request"""
        
        params = {
            'key': self.api_key,
            'cx': self.search_engine_id,
            'q': query,
            'start': start_index,
            'num': num_results
        }
        
        try:
            logger.info(f"Searching: '{query}' (start: {start_index})")
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # Rate limiting
            time.sleep(self.delay_between_requests + random.uniform(0.5, 1.5))
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Search API error for query '{query}': {str(e)}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error during search: {str(e)}")
            return {}
    
    def extract_domains_from_results(self, search_data: Dict) -> List[str]:
        """Extract clean domains from Google search results"""
        
        domains = []
        
        if 'items' not in search_data:
            return domains
            
        for item in search_data['items']:
            try:
                # Extract URL from search result
                url = item.get('link', '')
                if not url:
                    continue
                    
                # Parse domain
                parsed = urlparse(url)
                domain = parsed.netloc.lower()
                
                # Clean domain (remove www, subdomains for some cases)
                if domain.startswith('www.'):
                    domain = domain[4:]
                
                # Validate domain format
                if self.is_valid_domain(domain):
                    domains.append(domain)
                    
            except Exception as e:
                logger.warning(f"Error extracting domain from result: {str(e)}")
                continue
                
        return domains
    
    def is_valid_domain(self, domain: str) -> bool:
        """Validate domain format and filter out unwanted domains"""
        
        # Basic domain validation
        domain_pattern = re.compile(
            r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?'
            r'(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        )
        
        if not domain_pattern.match(domain):
            return False
            
        # Filter out unwanted domains
        excluded_patterns = [
            'google.com', 'youtube.com', 'facebook.com', 'twitter.com',
            'linkedin.com', 'reddit.com', 'stackoverflow.com',
            'github.com', 'wikipedia.org', 'amazon.com'
        ]
        
        for pattern in excluded_patterns:
            if pattern in domain:
                return False
                
        # Must have valid TLD
        if '.' not in domain:
            return False
            
        return True
    
    def discover_protected_sites(self, max_results_per_query: int = 50) -> Dict:
        """Main method to discover protected websites using all search queries"""
        
        logger.info("Starting protected sites discovery...")
        discovery_results = {
            'total_searches': 0,
            'total_results': 0,
            'unique_domains': set(),
            'query_results': {},
            'errors': []
        }
        
        for query in self.search_queries:
            logger.info(f"\n=== Processing query: '{query}' ===")
            query_domains = set()
            
            # Search multiple pages for each query
            for start_index in range(1, max_results_per_query + 1, 10):
                try:
                    search_data = self.execute_search(query, start_index, 10)
                    discovery_results['total_searches'] += 1
                    
                    if not search_data:
                        continue
                        
                    # Extract domains from this page
                    page_domains = self.extract_domains_from_results(search_data)
                    query_domains.update(page_domains)
                    discovery_results['total_results'] += len(page_domains)
                    
                    logger.info(f"Found {len(page_domains)} domains on page {(start_index-1)//10 + 1}")
                    
                    # Check if we have more results
                    if 'queries' not in search_data or 'nextPage' not in search_data['queries']:
                        logger.info(f"No more results for query: '{query}'")
                        break
                        
                except Exception as e:
                    error_msg = f"Error processing query '{query}' at index {start_index}: {str(e)}"
                    logger.error(error_msg)
                    discovery_results['errors'].append(error_msg)
                    continue
            
            # Store results for this query
            discovery_results['query_results'][query] = list(query_domains)
            discovery_results['unique_domains'].update(query_domains)
            
            logger.info(f"Query '{query}' completed: {len(query_domains)} unique domains")
        
        # Final summary
        total_unique = len(discovery_results['unique_domains'])
        logger.info(f"\n=== Discovery Complete ===")
        logger.info(f"Total searches executed: {discovery_results['total_searches']}")
        logger.info(f"Total results processed: {discovery_results['total_results']}")
        logger.info(f"Unique domains discovered: {total_unique}")
        
        return discovery_results
    
    def get_priority_test_sites(self) -> List[str]:
        """Get the 5 priority Cloudflare-protected sites that currently fail"""
        return [
            "canva.com",
            "udemy.com", 
            "kickstarter.com",
            "technewsworld.com",
            "cruisecritic.com"
        ]
    
    def prepare_test_corpus(self, max_new_sites: int = 200) -> Dict:
        """Prepare comprehensive test corpus combining existing cache + new discoveries"""
        
        logger.info("Preparing comprehensive test corpus...")
        
        # Get priority sites (known failures)
        priority_sites = self.get_priority_test_sites()
        
        # Discover new protected sites
        discovery_results = self.discover_protected_sites()
        new_domains = list(discovery_results['unique_domains'])
        
        # Combine and prioritize
        test_corpus = {
            'priority_sites': priority_sites,  # Test these first
            'new_discoveries': new_domains[:max_new_sites],  # Limit new sites
            'total_new_sites': len(new_domains),
            'discovery_summary': discovery_results
        }
        
        total_test_sites = len(priority_sites) + len(test_corpus['new_discoveries'])
        logger.info(f"Test corpus prepared: {total_test_sites} sites total")
        logger.info(f"- Priority sites: {len(priority_sites)}")
        logger.info(f"- New discoveries: {len(test_corpus['new_discoveries'])}")
        
        return test_corpus

if __name__ == "__main__":
    # Test the Google Search API integration
    search_api = CustomSearchAPI()
    
    # Prepare test corpus
    corpus = search_api.prepare_test_corpus(max_new_sites=50)
    
    print(f"\n=== Test Corpus Summary ===")
    print(f"Priority sites: {len(corpus['priority_sites'])}")
    print(f"New discoveries: {len(corpus['new_discoveries'])}")
    print(f"Total available: {corpus['total_new_sites']}")
    
    print(f"\nPriority sites to test:")
    for site in corpus['priority_sites']:
        print(f"  - {site}")
    
    print(f"\nFirst 10 new discoveries:")
    for site in corpus['new_discoveries'][:10]:
        print(f"  - {site}")
