[{"url": "https://canva.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:55:51.748444", "execution_time": 9.270400047302246, "error_message": "", "title": "Canva: Visual Suite for Everyone", "meta_description": "Canva is a free-to-use online graphic design tool. Use it to create social media posts, presentations, posters, videos, logos and more.", "word_count": 736, "paragraphs": 124, "headings": {"h1": 1, "h2": 17, "h3": 20, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 410, "internal": 0, "external": 0}, "images": {"total": 11, "with_alt": 11, "without_alt": 0}, "language": "en", "content_quality_score": 85, "javascript_errors": ["Console error: Failed to load resource: the server responded with a status of 403 ()", "Console error: Failed to load resource: the server responded with a status of 403 ()", "Console warning: [GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment", "Console error: Failed to load resource: the server responded with a status of 400 ()", "Console error: malformed JSON response: <html lang=\"en\" dir=ltr><meta charset=utf-8><meta name=viewport content=\"initial-scale=1, minimum-scale=1, width=device-width\"><title>Error 400 (Bad Request)!!1</title><style nonce=\"wHMZnWjp-Umytaw7iLGezg\">*{margin:0;padding:0}html,code{font:15px/22px arial,sans-serif}html{background:#fff;color:#222;padding:15px}body{color:#222;text-align:unset;margin:7% auto 0;max-width:390px;min-height:180px;padding:30px 0 15px;}* > body{background:url(//www.google.com/images/errors/robot.png) 100% 5px no-repeat;padding-right:205px}p{margin:11px 0 22px;overflow:hidden}pre{white-space:pre-wrap;}ins{color:#777;text-decoration:none}a img{border:0}@media screen and (max-width:772px){body{background:none;margin-top:0;max-width:none;padding-right:0}}#logo{background:url(//www.google.com/images/branding/googlelogo/1x/googlelogo_color_150x54dp.png) no-repeat;margin-left:-5px}@media only screen and (min-resolution:192dpi){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat 0% 0%/100% 100%;-moz-border-image:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) 0}}@media only screen and (-webkit-min-device-pixel-ratio:2){#logo{background:url(//www.google.com/images/branding/googlelogo/2x/googlelogo_color_150x54dp.png) no-repeat;-webkit-background-size:100% 100%}}#logo{display:inline-block;height:54px;width:150px}</style><main id=\"af-error-container\" role=\"main\"><a href=//www.google.com><span id=logo aria-label=Google role=img></span></a><p><b>400.</b> <ins>That’s an error.</ins><p>The server cannot process the request because it is malformed. It should not be retried. <ins>That’s all we know.</ins></main>", "da", "Console error: Failed to load resource: the server responded with a status of 403 ()", "Console error: Failed to load resource: the server responded with a status of 403 ()", "Console error: Failed to load resource: the server responded with a status of 403 ()", "Console error: Failed to load resource: the server responded with a status of 403 ()"], "network_requests": 141, "load_time": 9.250950813293457, "screenshot_path": "screenshots/canva.com_20250530_165559.png", "protection_bypassed": true, "protection_type": "cloudflare", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["Vue.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Cloudflare"], "frameworks_detected": ["Vue.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Cloudflare"], "social_media": ["Facebook", "Twitter", "Instagram"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 1, "total_tests": 15, "test_time": 9.270401954650879}, {"url": "https://udemy.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:56:03.021042", "execution_time": 8.620012044906616, "error_message": "", "title": "Online Courses - Learn Anything, On Your Schedule | Udemy", "meta_description": "Udemy is an online learning and teaching marketplace with over 250,000 courses and 80 million students. Learn programming, marketing, data science and more.", "word_count": 1431, "paragraphs": 40, "headings": {"h1": 5, "h2": 9, "h3": 23, "h4": 6, "h5": 0, "h6": 0}, "links": {"total": 291, "internal": 0, "external": 0}, "images": {"total": 57, "with_alt": 55, "without_alt": 2}, "language": "en", "content_quality_score": 90, "javascript_errors": ["Console warning: The mode button/widget are renamed to active/passive respectively and will be deprecated soon.", "Console error: Failed to load resource: net::ERR_NAME_NOT_RESOLVED", "Console error: Provider's accounts list is empty.", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment", "Console warning: The following feature variant assignments were not provided; please add them to an ExperimentationProvider: personal_plan_course_enrollment"], "network_requests": 179, "load_time": 8.60802412033081, "screenshot_path": "screenshots/udemy.com_20250530_165610.png", "protection_bypassed": true, "protection_type": "cloudflare", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap"], "frameworks_detected": ["React", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap"], "social_media": ["Twitter"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 2, "total_tests": 15, "test_time": 8.620023965835571}, {"url": "https://kickstarter.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:56:13.643079", "execution_time": 7.342203140258789, "error_message": "", "title": "Kickstarter", "meta_description": "Kickstarter exists to help bring creative projects to life. A home for film, music, art, theater, games, comics, design, photography, and more.", "word_count": 2110, "paragraphs": 325, "headings": {"h1": 1, "h2": 1, "h3": 53, "h4": 17, "h5": 0, "h6": 0}, "links": {"total": 1172, "internal": 0, "external": 0}, "images": {"total": 211, "with_alt": 211, "without_alt": 0}, "language": "en", "content_quality_score": 95, "javascript_errors": [], "network_requests": 356, "load_time": 7.3316969871521, "screenshot_path": "screenshots/kickstarter.com_20250530_165620.png", "protection_bypassed": true, "protection_type": "cloudflare", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Angular", "j<PERSON><PERSON><PERSON>"], "frameworks_detected": ["React", "Angular", "j<PERSON><PERSON><PERSON>"], "social_media": ["Facebook", "Twitter", "Instagram", "YouTube", "TikTok"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 3, "total_tests": 15, "test_time": 7.342217922210693}, {"url": "https://technewsworld.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:56:22.987883", "execution_time": 14.462896823883057, "error_message": "", "title": "TechNewsWorld - Technology News and Information", "meta_description": "Tech news, reviews and analysis of computing, enterprise IT, cybersecurity, mobile technology, cloud computing, tech industry trends, how-tos, digital marketing, and advertising advice.", "word_count": 1709, "paragraphs": 244, "headings": {"h1": 1, "h2": 17, "h3": 14, "h4": 98, "h5": 0, "h6": 10}, "links": {"total": 457, "internal": 0, "external": 0}, "images": {"total": 114, "with_alt": 112, "without_alt": 2}, "language": "en-US", "content_quality_score": 90, "javascript_errors": ["Console error: Error with Permissions-Policy header: Parse of permissions policy failed because of errors reported by structured header parser.", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.", "adsbygoogle.push() error: All 'ins' elements in the DOM with class=adsbygoogle already have ads in them."], "network_requests": 296, "load_time": 14.451408863067627, "screenshot_path": "screenshots/technewsworld.com_20250530_165636.png", "protection_bypassed": true, "protection_type": "cloudflare", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Vue.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "WordPress", "Shopify", "Cloudflare"], "frameworks_detected": ["React", "Vue.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "WordPress", "Shopify", "Cloudflare"], "social_media": ["Facebook", "Twitter", "LinkedIn"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 4, "total_tests": 15, "test_time": 14.462908267974854}, {"url": "https://cruisecritic.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:56:39.454572", "execution_time": 34.34388303756714, "error_message": "", "title": "cruisecritic.com", "meta_description": "", "word_count": 0, "paragraphs": 0, "headings": {"h1": 0, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 0, "internal": 0, "external": 0}, "images": {"total": 0, "with_alt": 0, "without_alt": 0}, "language": "unknown", "content_quality_score": 10, "javascript_errors": ["Console error: Failed to load resource: the server responded with a status of 403 ()", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing."], "network_requests": 11, "load_time": 34.32810020446777, "screenshot_path": "screenshots/cruisecritic.com_20250530_165713.png", "protection_bypassed": true, "protection_type": "<PERSON><PERSON>a", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": [], "frameworks_detected": [], "social_media": [], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 403, "test_number": 5, "total_tests": 15, "test_time": 34.34389019012451}, {"url": "https://example.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:57:15.801362", "execution_time": 33.44557571411133, "error_message": "", "title": "Example Domain", "meta_description": "", "word_count": 0, "paragraphs": 0, "headings": {"h1": 1, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 1, "internal": 0, "external": 0}, "images": {"total": 0, "with_alt": 0, "without_alt": 0}, "language": "unknown", "content_quality_score": 25, "javascript_errors": [], "network_requests": 1, "load_time": 33.43820095062256, "screenshot_path": "screenshots/example.com_20250530_165749.png", "protection_bypassed": true, "protection_type": "none_detected", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": [], "frameworks_detected": [], "social_media": [], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 6, "total_tests": 15, "test_time": 33.445582151412964}, {"url": "https://httpbin.org/get", "status": "FAIL", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:57:51.250650", "execution_time": 34.84230303764343, "error_message": "", "title": "", "meta_description": "", "word_count": 0, "paragraphs": 0, "headings": {"h1": 0, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 0, "internal": 0, "external": 0}, "images": {"total": 0, "with_alt": 0, "without_alt": 0}, "language": "unknown", "content_quality_score": 10, "javascript_errors": [], "network_requests": 1, "load_time": 34.84069228172302, "screenshot_path": "screenshots/httpbin.org_20250530_165826.png", "protection_bypassed": true, "protection_type": "none_detected", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": [], "frameworks_detected": [], "social_media": [], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 7, "total_tests": 15, "test_time": 34.84230899810791}, {"url": "https://github.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:58:28.095584", "execution_time": 6.484877824783325, "error_message": "", "title": "GitHub · Build and ship software on a single, collaborative platform · GitHub", "meta_description": "GitHub is where people build software. More than 150 million people use GitHub to discover, fork, and contribute to over 420 million projects.", "word_count": 1253, "paragraphs": 29, "headings": {"h1": 4, "h2": 11, "h3": 17, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 141, "internal": 0, "external": 0}, "images": {"total": 54, "with_alt": 54, "without_alt": 0}, "language": "en", "content_quality_score": 95, "javascript_errors": [], "network_requests": 140, "load_time": 6.480114936828613, "screenshot_path": "screenshots/github.com_20250530_165832.png", "protection_bypassed": true, "protection_type": "rate_limited", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Shopify"], "frameworks_detected": ["React", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "Shopify"], "social_media": ["Twitter", "Instagram", "LinkedIn", "YouTube", "TikTok"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 8, "total_tests": 15, "test_time": 6.484899044036865}, {"url": "https://stackoverflow.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:58:36.583306", "execution_time": 36.54256319999695, "error_message": "", "title": "Newest Questions - Stack Overflow", "meta_description": "", "word_count": 0, "paragraphs": 0, "headings": {"h1": 7, "h2": 1, "h3": 18, "h4": 1, "h5": 5, "h6": 0}, "links": {"total": 254, "internal": 0, "external": 0}, "images": {"total": 17, "with_alt": 17, "without_alt": 0}, "language": "unknown", "content_quality_score": 40, "javascript_errors": ["Console warning: Error with Feature-Policy header: Unrecognized feature: 'speaker'.", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.", "Console warning: An iframe which has both allow-scripts and allow-same-origin for its sandbox attribute can escape its sandboxing.", "Console warning: The mode button/widget are renamed to active/passive respectively and will be deprecated soon.", "Console error: Not signed in with the identity provider."], "network_requests": 95, "load_time": 36.536773920059204, "screenshot_path": "screenshots/stackoverflow.com_20250530_165912.png", "protection_bypassed": true, "protection_type": "cloudflare", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Vue.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "WordPress"], "frameworks_detected": ["React", "Vue.js", "Angular", "j<PERSON><PERSON><PERSON>", "Bootstrap", "WordPress"], "social_media": ["Facebook", "Twitter", "Instagram", "LinkedIn"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 9, "total_tests": 15, "test_time": 36.54257106781006}, {"url": "https://news.ycombinator.com", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:59:15.128256", "execution_time": 33.8286337852478, "error_message": "", "title": "Hacker News", "meta_description": "", "word_count": 0, "paragraphs": 0, "headings": {"h1": 0, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 226, "internal": 0, "external": 0}, "images": {"total": 3, "with_alt": 0, "without_alt": 3}, "language": "unknown", "content_quality_score": 45, "javascript_errors": [], "network_requests": 6, "load_time": 33.82666492462158, "screenshot_path": "screenshots/news.ycombinator.com_20250530_165948.png", "protection_bypassed": true, "protection_type": "none_detected", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["Angular", "WordPress"], "frameworks_detected": ["Angular", "WordPress"], "social_media": ["YouTube"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 10, "total_tests": 15, "test_time": 33.82863903045654}, {"url": "https://reddit.com", "status": "FAIL", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T16:59:50.961200", "execution_time": 30.070492267608643, "error_message": "Navigation failed: Page.goto: Timeout 30000ms exceeded.\nCall log:\n  - navigating to \"https://reddit.com/\", waiting until \"networkidle\"\n", "title": "", "meta_description": "", "word_count": 0, "paragraphs": 0, "headings": {"h1": 0, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 0, "internal": 0, "external": 0}, "images": {"total": 0, "with_alt": 0, "without_alt": 0}, "language": "unknown", "content_quality_score": 0, "javascript_errors": [], "network_requests": 0, "load_time": 0, "screenshot_path": "", "protection_bypassed": false, "protection_type": "unknown", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": [], "frameworks_detected": [], "social_media": [], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "test_number": 11, "total_tests": 15, "test_time": 30.070510625839233}, {"url": "https://python.org", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T17:00:23.035207", "execution_time": 4.699645757675171, "error_message": "", "title": "Welcome to Python.org", "meta_description": "The official home of the Python Programming Language", "word_count": 584, "paragraphs": 37, "headings": {"h1": 6, "h2": 9, "h3": 1, "h4": 6, "h5": 0, "h6": 0}, "links": {"total": 220, "internal": 0, "external": 0}, "images": {"total": 1, "with_alt": 1, "without_alt": 0}, "language": "en", "content_quality_score": 85, "javascript_errors": ["No ad placements found."], "network_requests": 36, "load_time": 4.696382999420166, "screenshot_path": "screenshots/python.org_20250530_170027.png", "protection_bypassed": true, "protection_type": "none_detected", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["Angular", "j<PERSON><PERSON><PERSON>"], "frameworks_detected": ["Angular", "j<PERSON><PERSON><PERSON>"], "social_media": ["Twitter", "LinkedIn"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 12, "total_tests": 15, "test_time": 4.699650049209595}, {"url": "https://nodejs.org", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T17:00:29.737315", "execution_time": 4.200921058654785, "error_message": "", "title": "Node.js — Run JavaScript Everywhere", "meta_description": "Node.js® is a JavaScript runtime built on Chrome's V8 JavaScript engine.", "word_count": 162, "paragraphs": 2, "headings": {"h1": 1, "h2": 0, "h3": 0, "h4": 0, "h5": 0, "h6": 0}, "links": {"total": 29, "internal": 0, "external": 0}, "images": {"total": 0, "with_alt": 0, "without_alt": 0}, "language": "en-GB", "content_quality_score": 75, "javascript_errors": [], "network_requests": 46, "load_time": 4.196532964706421, "screenshot_path": "screenshots/nodejs.org_20250530_170033.png", "protection_bypassed": true, "protection_type": "rate_limited", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Angular", "j<PERSON><PERSON><PERSON>"], "frameworks_detected": ["React", "Angular", "j<PERSON><PERSON><PERSON>"], "social_media": ["Twitter", "LinkedIn"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 13, "total_tests": 15, "test_time": 4.200927972793579}, {"url": "https://react.dev", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T17:00:35.940365", "execution_time": 4.907036781311035, "error_message": "", "title": "React", "meta_description": "React is the library for web and native user interfaces. Build user interfaces out of individual pieces called components written in JavaScript. React is designed to let you seamlessly combine components written by independent people, teams, and organizations.", "word_count": 975, "paragraphs": 48, "headings": {"h1": 2, "h2": 15, "h3": 32, "h4": 2, "h5": 0, "h6": 0}, "links": {"total": 99, "internal": 0, "external": 0}, "images": {"total": 43, "with_alt": 43, "without_alt": 0}, "language": "en", "content_quality_score": 85, "javascript_errors": ["Console warning: The resource https://react.dev/images/home/<USER>/cover.svg was preloaded using link preload but not used within a few seconds from the window's load event. Please make sure it has an appropriate `as` value and it is preloaded intentionally."], "network_requests": 61, "load_time": 4.901360034942627, "screenshot_path": "screenshots/react.dev_20250530_170039.png", "protection_bypassed": true, "protection_type": "rate_limited", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Angular", "j<PERSON><PERSON><PERSON>"], "frameworks_detected": ["React", "Angular", "j<PERSON><PERSON><PERSON>"], "social_media": ["Facebook", "Twitter", "YouTube"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 14, "total_tests": 15, "test_time": 4.907040119171143}, {"url": "https://vuejs.org", "status": "PASS", "method": "playwright", "browser_engine": "chromium", "timestamp": "2025-05-30T17:00:42.849912", "execution_time": 3.6466808319091797, "error_message": "", "title": "Vue.js - The Progressive JavaScript Framework | Vue.js", "meta_description": "Vue.js - The Progressive JavaScript Framework", "word_count": 190, "paragraphs": 13, "headings": {"h1": 1, "h2": 5, "h3": 0, "h4": 8, "h5": 0, "h6": 0}, "links": {"total": 159, "internal": 0, "external": 0}, "images": {"total": 43, "with_alt": 43, "without_alt": 0}, "language": "en-US", "content_quality_score": 85, "javascript_errors": [], "network_requests": 77, "load_time": 3.644968032836914, "screenshot_path": "screenshots/vuejs.org_20250530_170046.png", "protection_bypassed": true, "protection_type": "none_detected", "spa_detected": false, "ajax_content": 0, "modal_dialogs": 0, "technology_stack": ["React", "Vue.js", "Angular", "Bootstrap"], "frameworks_detected": ["React", "Vue.js", "Angular", "Bootstrap"], "social_media": ["Twitter", "YouTube"], "emails": [], "phone_numbers": [], "structured_data": false, "readability_score": 0, "status_code": 200, "test_number": 15, "total_tests": 15, "test_time": 3.6466867923736572}]